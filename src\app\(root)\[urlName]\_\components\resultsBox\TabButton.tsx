"use client";

type Props = {
  value: string;
  label: string;
  isActive: boolean;
  onClick: (value: string) => void;
  className?: string;
};

export default function TabButton({
  isActive,
  label,
  onClick,
  value,
  className = "",
}: Props) {
  return (
    <button
      onClick={() => onClick(value)}
      className={`
        relative
        font-semibold
        px-1.5 sm:px-3 md:px-4 lg:px-6
        py-1.5 sm:py-2 lg:py-2.5
        rounded-t-lg
        text-xs sm:text-sm
        border border-gray-200
        transition-colors duration-150 ease-in-out
        ${className}
        ${
          isActive
            ? "bg-white text-primary border-b-white z-10"
            : "bg-gray-50 text-secondary/70 hover:bg-gray-100 hover:text-secondary/90 border-b-gray-200"
        }
      `}
    >
      {label}
    </button>
  );
}
