"use client";
import { CrossIcon } from "@/ui/icons/general";
import NavLogo from "./NavLogo";
import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import NavButtons from "./NavButtons";
import { DownIcon } from "@/ui/icons/navigation";
import Link from "next/link";
import { LazyMotion, domAnimation, m } from "framer-motion";
import useScrollLock from "@/hooks/useScrollLock";

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  menuItems: {
    label: string;
    href: string;
    isDropdown?: boolean;
    isSpecial?: boolean;
    subMenus?: {
      label: string;
      href: string;
    }[];
  }[];
};

export default function SidebarMenu({ open, setOpen, menuItems }: Props) {
  const [isMounted, setIsMounted] = useState(false);

  // Lock background scroll when sidebar is open
  useScrollLock(open);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle escape key to close sidebar
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && open) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [open, setOpen]);

  if (isMounted)
    return createPortal(
      <LazyMotion features={domAnimation}>
        <div
          className={`${
            open ? "translate-x-0" : "translate-x-full"
          } duration-300 w-full lg:hidden fixed top-0 right-0 h-full overflow-y-auto bg-light-gray-6 p-4 z-50`}
          role="dialog"
          aria-modal="true"
          aria-label="Mobile navigation menu"
          id="mobile-navigation"
        >
          <div className="w-full flex items-center justify-between pb-4">
            <NavLogo />
            <button
              onClick={() => setOpen(false)}
              aria-label="Close navigation menu"
              type="button"
            >
              <CrossIcon className="w-6 h-6 text-secondary" />
            </button>
          </div>

          <NavButtons />

          <nav role="navigation" aria-label="Mobile navigation">
            <ul className="mt-4">
              {menuItems.map((item, index) => (
                <SidebarMenuItem
                  key={index}
                  item={item}
                  onCloseSide={() => setOpen(false)}
                />
              ))}
            </ul>
          </nav>
        </div>
      </LazyMotion>,
      document.body
    );
}

function SidebarMenuItem({
  item,
  onCloseSide,
}: {
  item: Props["menuItems"][0];
  onCloseSide: () => void;
}) {
  const [open, setOpen] = useState(false);

  // Special styling for "Go Pro" link
  if (item.isSpecial) {
    return (
      <li className="py-4 border-t border-light-gray w-full overflow-hidden">
        <m.div
          className="w-full max-w-full"
          whileHover={{
            scale: 1.02,
          }}
          whileTap={{
            scale: 0.95,
            rotate: -2,
          }}
          transition={{
            scale: { type: "spring", stiffness: 300, damping: 15 },
            rotate: { duration: 0.3 },
          }}
        >
          <m.span
            className="block w-full max-w-full p-2.5 text-primary font-bold overflow-hidden"
            whileHover={{
              color: "rgba(145, 74, 196, 1)",
              letterSpacing: "0.05em",
            }}
            transition={{
              letterSpacing: { duration: 0.3 },
            }}
          >
            <Link
              href={item.href}
              onClick={onCloseSide}
              aria-label={`${item.label} - Premium features`}
            >
              {item.label}
            </Link>
          </m.span>
        </m.div>
      </li>
    );
  }

  return (
    <li className="border-t border-light-gray py-4 w-full overflow-hidden">
      <div className="w-full flex items-center justify-between leading-5 hover:text-primary duration-300 ease-in-out">
        <Link
          href={item.href}
          className="inline-block p-2.5"
          onClick={onCloseSide}
          aria-label={item.label}
        >
          {item.label}
        </Link>
        {item.isDropdown && (
          <button
            onClick={() => setOpen(!open)}
            aria-label={`${open ? "Collapse" : "Expand"} ${item.label} submenu`}
            aria-expanded={open}
            type="button"
          >
            <DownIcon
              className={`${
                open ? "rotate-180" : "rotate-0"
              } duration-300 ease-in-out`}
              aria-hidden="true"
            />
          </button>
        )}
      </div>
      <div
        className={`${
          open ? "max-h-screen duration-400 mt-2" : "max-h-0 duration-300"
        } overflow-hidden ease-in-out`}
        role={item.isDropdown ? "region" : undefined}
        aria-label={item.isDropdown ? `${item.label} submenu` : undefined}
      >
        <ul className="flex flex-col items-start gap-2">
          {item.subMenus?.map((subItem, index) => (
            <li key={index}>
              <Link
                href={subItem.href}
                onClick={onCloseSide}
                className="inline-block p-2.5 px-6 text-sm text-secondary"
                aria-label={subItem.label}
              >
                {subItem.label}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </li>
  );
}
