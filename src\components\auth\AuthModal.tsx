"use client";
import { useEffect, useState, useCallback } from "react";
import Modal from "@/ui/Modal";
import LoginForm from "./LoginForm";
import RegisterForm from "./RegisterForm";
import OtpVerificationForm from "./OtpVerificationForm";
import StepProgressBar from "@/ui/StepProgressBar";
import { InfoIcon } from "@/ui/icons/general";
import { useAuthStore } from "@/store/authStore";
import { motion } from "framer-motion";
import {
  AuthState,
  AuthEvent,
  transition,
  getAuthModalView,
} from "@/utils/authStateMachine";

export default function AuthModal() {
  // Use specific selectors for better performance
  const isAuthModalOpen = useAuthStore((state) => state.isAuthModalOpen);
  const isAuthModalLoading = useAuthStore((state) => state.isAuthModalLoading);
  const shouldShowAuthModal = useAuthStore(
    (state) => state.shouldShowAuthModal
  );
  const authModalView = useAuthStore((state) => state.authModalView);
  const authModalEmail = useAuthStore((state) => state.authModalEmail);
  const authModalActionType = useAuthStore(
    (state) => state.authModalActionType
  );
  const authModalShowSteps = useAuthStore((state) => state.authModalShowSteps);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const error = useAuthStore((state) => state.error);

  // Get actions from auth store
  const setAuthModalView = useAuthStore((state) => state.setAuthModalView);
  const setAuthModalEmail = useAuthStore((state) => state.setAuthModalEmail);
  const closeAuthModal = useAuthStore((state) => state.closeAuthModal);
  const handleAuthSuccess = useAuthStore((state) => state.handleAuthSuccess);

  // State machine state
  const [currentState, setCurrentState] = useState<AuthState>("idle");

  // Form state management
  const [shouldPreventModalClose, setShouldPreventModalClose] = useState<
    (() => boolean) | null
  >(null);

  // Handle form state changes from child components
  const handleFormStateChange = useCallback((preventCloseFn: () => boolean) => {
    setShouldPreventModalClose(() => preventCloseFn);
  }, []);

  // Handle state transitions
  const send = (event: AuthEvent) => {
    const nextState = transition(currentState, event);
    console.log(
      `Auth state transition: ${currentState} -> ${nextState} (event: ${event.type})`
    );
    setCurrentState(nextState);

    // Update the modal view based on the new state
    setAuthModalView(getAuthModalView(nextState));

    // Handle special actions based on state transitions
    if (nextState === "success") {
      handleAuthSuccess();
    } else if (nextState === "idle") {
      closeAuthModal();
    }
  };

  // Initialize state machine when modal opens
  useEffect(() => {
    if (isAuthModalOpen) {
      // Start in the checking state
      setCurrentState("checking");

      // Check authentication status
      if (isAuthenticated) {
        send({ type: "AUTHENTICATED" });
      } else if (!isAuthModalLoading && shouldShowAuthModal) {
        send({ type: "UNAUTHENTICATED" });
      }
    }
  }, [
    isAuthModalOpen,
    isAuthenticated,
    isAuthModalLoading,
    shouldShowAuthModal,
  ]);

  // Cleanup effect when component unmounts
  useEffect(() => {
    return () => {
      // Clean up any resources when component unmounts
      if (isAuthModalOpen) {
        closeAuthModal();
      }
    };
  }, []);

  // Define steps for each flow - with all registration/login processes inside Authentication step
  const registerSteps = [
    { number: 1, label: "Authentication" },
    {
      number: 2,
      label:
        authModalActionType === "pdf"
          ? "Download"
          : authModalActionType === "whiteLabel"
          ? "White Label Setting"
          : "Share",
    },
    ...(authModalActionType === "whiteLabel"
      ? [
          { number: 3, label: "Billing" },
          { number: 4, label: "Payment" },
          { number: 5, label: "Download" },
        ]
      : []),
  ];

  const loginSteps = [
    { number: 1, label: "Authentication" },
    {
      number: 2,
      label:
        authModalActionType === "pdf"
          ? "Download"
          : authModalActionType === "whiteLabel"
          ? "White Label Setting"
          : "Share",
    },
    ...(authModalActionType === "whiteLabel"
      ? [
          { number: 3, label: "Billing" },
          { number: 4, label: "Payment" },
          { number: 5, label: "Download" },
        ]
      : []),
  ];

  // Get current step number based on the auth step
  const getCurrentStepNumber = () => {
    // All authentication steps (login-register, register, login, otp) are part of step 1
    if (authModalView === "success") {
      return 2; // Success means we're on the next step (Download/Share/White Label)
    } else {
      return 1; // All other steps are part of Authentication
    }
  };

  // Display error message if there's an error in the store
  const renderError = () => {
    if (!error) return null;

    return (
      <div className="px-4 py-2 mb-4 bg-primary-red/10 border border-primary-red/20 rounded-md">
        <p className="text-primary-red text-xs md:text-sm text-center">
          {error}
        </p>
      </div>
    );
  };

  const renderModalContent = () => {
    switch (authModalView) {
      case "login-register":
        return {
          title: "Authentication Required",
          children: (
            <div className="flex flex-col h-full p-4 lg:p-6">
              <div className="flex-1 flex flex-col items-center justify-center">
                <div className="w-14 h-14 md:w-16 md:h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4 md:mb-6">
                  <InfoIcon className="text-purple-600 w-8 h-8 md:w-10 md:h-10" />
                </div>
                <h2 className="text-xl md:text-2xl font-bold text-secondary text-center mb-2 md:mb-3">
                  Authentication Required
                </h2>
                <p className="text-sm md:text-base text-secondary/70 text-center mb-6 md:mb-8 max-w-md px-1">
                  {authModalActionType === "pdf"
                    ? "Please login or create an account to download the PDF report with detailed SEO analysis."
                    : authModalActionType === "share"
                    ? "Please login or create an account to share this SEO analysis report with others."
                    : authModalActionType === "whiteLabel"
                    ? "Please login or create an account to access White Label features."
                    : "Please login or create an account to continue."}
                </p>
                <div className="flex flex-col w-full max-w-sm gap-3 md:gap-4">
                  <motion.button
                    className="btn btn--primary !w-full !py-3 md:!py-4 !text-sm md:!text-base font-semibold flex items-center justify-center gap-2 transition-all"
                    onClick={() => send({ type: "LOGIN" })}
                    whileHover={{
                      scale: 1.005,
                      boxShadow: "0 4px 12px rgba(145, 74, 196, 0.25)",
                      backgroundColor: "rgba(155, 84, 206, 1)",
                    }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.15 }}
                  >
                    <span>Login to Your Account</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-4 h-4 md:w-5 md:h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M13.8 12H3" />
                    </svg>
                  </motion.button>
                  <motion.button
                    className="btn btn--outline !w-full !py-3 md:!py-4 !text-sm md:!text-base font-semibold flex items-center justify-center gap-2 transition-all"
                    onClick={() => send({ type: "REGISTER" })}
                    whileHover={{
                      scale: 1.005,
                      backgroundColor: "var(--color-primary)",
                      color: "white",
                      boxShadow: "0 4px 12px rgba(145, 74, 196, 0.25)",
                    }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.15 }}
                  >
                    <span>Create New Account</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-4 h-4 md:w-5 md:h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="8.5" cy="7" r="4"></circle>
                      <line x1="20" y1="8" x2="20" y2="14"></line>
                      <line x1="23" y1="11" x2="17" y2="11"></line>
                    </svg>
                  </motion.button>
                </div>
              </div>
              <div className="mt-auto pt-4 md:pt-6 text-center text-[10px] md:text-xs text-secondary/50">
                Your information is secure and will only be used to enhance your
                experience.
              </div>
            </div>
          ),
        };

      case "login":
        return {
          title: "Login to Your Account",
          children: (
            <LoginForm
              onSuccess={() => send({ type: "SUCCESS" })}
              onRegisterClick={() => send({ type: "REGISTER" })}
              onFormStateChange={handleFormStateChange}
            />
          ),
        };

      case "register":
        return {
          title: "Create New Account",
          children: (
            <RegisterForm
              onSuccess={(userEmail: string) => {
                setAuthModalEmail(userEmail);
                send({ type: "SUCCESS" });
              }}
              onLoginClick={() => send({ type: "LOGIN" })}
              onFormStateChange={handleFormStateChange}
            />
          ),
        };

      case "otp":
        return {
          title: "Verify Your Account",
          children: (
            <OtpVerificationForm
              email={authModalEmail}
              onSuccess={() => send({ type: "SUCCESS" })}
              onResendCode={() => {
                // This is just a callback for any additional handling needed after resend
                // The actual resend API call is handled in the OtpVerificationForm component
              }}
            />
          ),
        };

      case "success":
        return {
          title: "Success",
          children: (
            <div className="p-4 lg:p-6 flex-1 flex flex-col h-full">
              <div className="flex-1 flex flex-col items-center justify-center">
                <div className="w-14 h-14 md:w-16 md:h-16 bg-primary-green/10 rounded-full flex items-center justify-center mb-4 md:mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-7 h-7 md:w-8 md:h-8 text-primary-green"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <h2 className="text-xl md:text-2xl font-bold text-secondary text-center mb-2 md:mb-3">
                  {isAuthenticated
                    ? "You're Logged In"
                    : "Verification Successful"}
                </h2>
                <p className="text-sm md:text-base text-secondary/70 text-center mb-6 md:mb-8 max-w-md px-1">
                  {authModalActionType === "pdf"
                    ? "Your account has been successfully verified. You can now download the PDF report with detailed SEO analysis."
                    : authModalActionType === "share"
                    ? "Your account has been successfully verified. You can now share this report with detailed SEO analysis."
                    : authModalActionType === "whiteLabel"
                    ? "Your account has been successfully verified. You can now access White Label features."
                    : "Your account has been successfully verified. You can now continue."}
                </p>
                <div className="w-full max-w-sm">
                  <motion.button
                    onClick={() => send({ type: "CLOSE" })}
                    className="btn btn--primary !w-full !py-3 md:!py-4 !text-sm md:!text-base font-semibold flex items-center justify-center gap-2 transition-all"
                    whileHover={{
                      scale: 1.005,
                      boxShadow: "0 4px 12px rgba(145, 74, 196, 0.25)",
                      backgroundColor: "rgba(155, 84, 206, 1)",
                    }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.15 }}
                  >
                    {authModalActionType === "pdf" ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-4 h-4 md:w-5 md:h-5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-4 h-4 md:w-5 md:h-5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <circle cx="18" cy="5" r="3"></circle>
                        <circle cx="6" cy="12" r="3"></circle>
                        <circle cx="18" cy="19" r="3"></circle>
                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                      </svg>
                    )}
                    {authModalActionType === "pdf"
                      ? "Continue to PDF"
                      : authModalActionType === "share"
                      ? "Share Report"
                      : authModalActionType === "whiteLabel"
                      ? "Continue to White Label"
                      : "Continue"}
                  </motion.button>
                </div>
              </div>
            </div>
          ),
        };

      default:
        return {
          title: "Error",
          children: (
            <div className="p-4 lg:p-6 flex-1 flex flex-col items-center justify-center">
              <div className="w-14 h-14 md:w-16 md:h-16 bg-primary-red/10 rounded-full flex items-center justify-center mb-4 md:mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-7 h-7 md:w-8 md:h-8 text-primary-red"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
              </div>
              <h2 className="text-xl md:text-2xl font-bold text-secondary text-center mb-2 md:mb-3">
                Something Went Wrong
              </h2>
              <p className="text-sm md:text-base text-secondary/70 text-center mb-6 md:mb-8 max-w-md px-1">
                {error || "An unexpected error occurred. Please try again."}
              </p>
              <motion.button
                onClick={() => send({ type: "CLOSE" })}
                className="btn btn--primary !py-2 md:!py-3 !text-sm md:!text-base font-semibold"
                whileHover={{
                  scale: 1.005,
                  boxShadow: "0 4px 12px rgba(145, 74, 196, 0.25)",
                  backgroundColor: "rgba(155, 84, 206, 1)",
                }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.15 }}
              >
                Close
              </motion.button>
            </div>
          ),
        };
    }
  };

  // Loading indicator for when authentication check is in progress
  if (isAuthModalOpen && isAuthModalLoading) {
    return (
      <Modal
        open={true}
        onClose={() => send({ type: "CLOSE" })}
        title="Loading"
        size="md"
        shouldPreventClose={shouldPreventModalClose || undefined}
      >
        <div className="flex flex-col items-center justify-center p-8">
          <div className="relative w-16 h-16">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center opacity-30">
              <div
                className="w-16 h-16 border-4 border-primary/30 border-t-transparent rounded-full animate-spin"
                style={{ animationDuration: "1.5s" }}
              ></div>
            </div>
          </div>
          <p className="text-secondary text-center mt-4 font-medium">
            Checking authentication status
            <motion.span
              animate={{ opacity: [0, 1, 0] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            >
              ...
            </motion.span>
          </p>
          {error && (
            <p className="text-primary-red text-sm mt-4 text-center">{error}</p>
          )}
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      open={isAuthModalOpen && shouldShowAuthModal}
      onClose={() => send({ type: "CLOSE" })}
      title={renderModalContent().title}
      size="sm"
      shouldPreventClose={shouldPreventModalClose || undefined}
    >
      <div className="flex flex-col">
        {/* Display any global errors */}
        {renderError()}

        {/* Only show step indicator when showSteps is true and not on the initial choice screen */}
        {authModalShowSteps && authModalView !== "login-register" && (
          <div className="px-3 pt-3 lg:px-6 lg:pt-6">
            {/* Use the new StepProgressBar for a more modern look */}
            <StepProgressBar
              steps={
                isAuthenticated
                  ? [
                      {
                        id: "success",
                        label:
                          authModalActionType === "pdf"
                            ? "Download"
                            : authModalActionType === "share"
                            ? "Share"
                            : authModalActionType === "whiteLabel"
                            ? "White Label"
                            : "Success",
                      },
                    ]
                  : authModalView === "register" || authModalView === "otp"
                  ? registerSteps.map((step) => ({
                      id: step.number.toString(),
                      label: step.label,
                    }))
                  : loginSteps.map((step) => ({
                      id: step.number.toString(),
                      label: step.label,
                    }))
              }
              currentStepId={
                isAuthenticated ? "success" : getCurrentStepNumber().toString()
              }
              className="mb-4"
            />
          </div>
        )}
        {renderModalContent().children}
      </div>
    </Modal>
  );
}
