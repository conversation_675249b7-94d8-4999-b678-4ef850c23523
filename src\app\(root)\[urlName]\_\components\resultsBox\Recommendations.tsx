"use client";
import BoxPrimary from "../BoxPrimary";
import { useState, useEffect } from "react";
import TabButton from "./TabButton";
import InfoCard from "../InfoCard";
import { motion, AnimatePresence } from "framer-motion";
import {
  OnPageAnalysis,
  UsabilityAnalysis,
  TechSEOAnalysis,
  SocialAnalysis,
  PerformanceAnalysis,
  LinksAnalysis,
  PageSpeedAnalysis,
  PageSpeedMobileAnalysis,
  RecommendationType,
} from "@/types/seoAnalyzerTypes";

// Import flag icons from react-icons for priority indicators
import { FiFlag } from "react-icons/fi";

// Define types for recommendations
type RecommendationBadge = {
  label: string;
  style: string;
};

// Use the imported RecommendationType
type Recommendation = {
  desc: string | RecommendationType;
  badges: RecommendationBadge[];
  source: string;
  score?: number;
};

// Using imported types from seoAnalyzerTypes.ts

// Define the props type using the imported types
type RecommendationsProps = {
  onPageSeoData?: OnPageAnalysis | null;
  usabilityData?: UsabilityAnalysis | null;
  linksData?: LinksAnalysis | null;
  techseoData?: TechSEOAnalysis | null; // Field name changed from technology_review_analysis to technology_review_analysis
  socialData?: SocialAnalysis | null;
  performanceData?: PerformanceAnalysis | null;
  pagespeedData?: PageSpeedAnalysis | null;
  pagespeedMobileData?: PageSpeedMobileAnalysis | null;
};

// Helper function to determine priority based on score
const getPriorityBadge = (score?: number): RecommendationBadge => {
  if (score === undefined)
    return { label: "Medium Priority", style: "badge--warning" };

  if (score <= 3) return { label: "High Priority", style: "badge--danger" };
  if (score <= 6) return { label: "Medium Priority", style: "badge--warning" };
  return { label: "Low Priority", style: "badge--success" };
};

// Helper function to get category badge
const getCategoryBadge = (category: string): RecommendationBadge => {
  return { label: category, style: "" };
};

export default function Recommendations({
  onPageSeoData,
  usabilityData,
  linksData,
  techseoData,
  socialData,
  performanceData,
  pagespeedData,
  pagespeedMobileData,
}: RecommendationsProps) {
  // Default to "All" tab
  const [activeTab, setActiveTab] = useState<string>("All");
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);

  // Function to extract recommendations from data
  useEffect(() => {
    const extractedRecommendations: Recommendation[] = [];

    // Extract from onPageSeoData - only if there are actual issues (pass: false)
    if (onPageSeoData) {
      // Title tag recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.title_tag?.recommendation &&
        !onPageSeoData.title_tag?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.title_tag.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.title_tag.score),
          ],
          source: "Title Tag",
        });
      }

      // Meta description recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.meta_description?.recommendation &&
        !onPageSeoData.meta_description?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.meta_description.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.meta_description.score),
          ],
          source: "Meta Description",
        });
      }

      // SERP preview recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.serp_preview?.recommendation &&
        !onPageSeoData.serp_preview?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.serp_preview.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.serp_preview.score),
          ],
          source: "SERP Preview",
        });
      }

      // Language recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.language?.recommendation &&
        !onPageSeoData.language?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.language.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.language.score),
          ],
          source: "Language",
        });
      }

      // Headers recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.headers?.hierarchy_recommendation &&
        !onPageSeoData.headers?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.headers.hierarchy_recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.headers.score),
          ],
          source: "Headers",
        });
      }

      // Keyword consistency recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.keyword_consistency?.recommendation &&
        !onPageSeoData.keyword_consistency?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.keyword_consistency.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.keyword_consistency?.score),
          ],
          source: "Keyword Consistency",
        });
      }

      // Content amount recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.content_amount?.recommendation &&
        !onPageSeoData.content_amount?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.content_amount.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.content_amount.score),
          ],
          source: "Content Amount",
        });
      }

      // Image alt attributes recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.image_alt_attributes?.recommendation &&
        !onPageSeoData.image_alt_attributes?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.image_alt_attributes.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.image_alt_attributes.score),
          ],
          source: "Image Alt Attributes",
        });
      }

      // Schema markup recommendations - only if failed or has meaningful recommendation
      if (
        onPageSeoData.schema_markup?.recommendation &&
        !onPageSeoData.schema_markup?.pass
      ) {
        extractedRecommendations.push({
          desc: onPageSeoData.schema_markup.recommendation,
          badges: [
            getCategoryBadge("On-Page SEO"),
            getPriorityBadge(onPageSeoData.schema_markup.score),
          ],
          source: "Schema Markup",
        });
      }
    }

    // Extract from usabilityData - only if there are actual issues (pass: false)
    if (usabilityData) {
      // Device rendering recommendations - only if failed or has meaningful recommendation
      if (
        usabilityData.device_rendering?.recommendation &&
        !usabilityData.device_rendering?.pass
      ) {
        extractedRecommendations.push({
          desc: usabilityData.device_rendering.recommendation,
          badges: [
            getCategoryBadge("Usability"),
            getPriorityBadge(usabilityData.device_rendering.score),
          ],
          source: "Device Rendering",
        });
      }

      // Viewport usage recommendations - only if failed or has meaningful recommendation
      if (
        usabilityData.viewport_usage?.recommendation &&
        !usabilityData.viewport_usage?.pass
      ) {
        extractedRecommendations.push({
          desc: usabilityData.viewport_usage.recommendation,
          badges: [
            getCategoryBadge("Usability"),
            getPriorityBadge(usabilityData.viewport_usage.score),
          ],
          source: "Viewport Usage",
        });
      }

      // Flash usage recommendations - only if failed or has meaningful recommendation
      if (
        usabilityData.flash_usage?.recommendation &&
        !usabilityData.flash_usage?.pass
      ) {
        extractedRecommendations.push({
          desc: usabilityData.flash_usage.recommendation,
          badges: [
            getCategoryBadge("Usability"),
            getPriorityBadge(usabilityData.flash_usage.score),
          ],
          source: "Flash Usage",
        });
      }

      // iFrames usage recommendations - only if failed or has meaningful recommendation
      if (
        usabilityData.iframes_usage?.recommendation &&
        !usabilityData.iframes_usage?.pass
      ) {
        extractedRecommendations.push({
          desc: usabilityData.iframes_usage.recommendation,
          badges: [
            getCategoryBadge("Usability"),
            getPriorityBadge(usabilityData.iframes_usage.score),
          ],
          source: "iFrames Usage",
        });
      }

      // Font legibility recommendations - only if failed or has meaningful recommendation
      if (
        usabilityData.font_legibility?.recommendation &&
        !usabilityData.font_legibility?.pass
      ) {
        extractedRecommendations.push({
          desc: usabilityData.font_legibility.recommendation,
          badges: [
            getCategoryBadge("Usability"),
            getPriorityBadge(usabilityData.font_legibility.score),
          ],
          source: "Font Legibility",
        });
      }

      // Tap target sizing recommendations - only if failed or has meaningful recommendation
      if (
        usabilityData.tap_target_sizing?.recommendation &&
        !usabilityData.tap_target_sizing?.pass
      ) {
        extractedRecommendations.push({
          desc: usabilityData.tap_target_sizing.recommendation,
          badges: [
            getCategoryBadge("Usability"),
            getPriorityBadge(usabilityData.tap_target_sizing.score),
          ],
          source: "Tap Target Sizing",
        });
      }
    }

    // Extract from linksData - only if there are actual issues (pass: false)
    if (linksData) {
      // Backlinks recommendations - only if failed or has meaningful recommendation
      if (linksData.backlinks?.recommendation && !linksData.backlinks?.pass) {
        extractedRecommendations.push({
          desc: linksData.backlinks.recommendation,
          badges: [getCategoryBadge("Backlinks"), getPriorityBadge()],
          source: "Backlinks",
        });
      }

      // Competitors recommendations - only if failed or has meaningful recommendation
      if (
        linksData.competitors?.recommendation &&
        !linksData.competitors?.pass
      ) {
        extractedRecommendations.push({
          desc: linksData.competitors.recommendation,
          badges: [getCategoryBadge("Backlinks"), getPriorityBadge()],
          source: "Competitors",
        });
      }

      // Mentions recommendations - only if failed or has meaningful recommendation
      if (linksData.mentions?.recommendation && !linksData.mentions?.pass) {
        extractedRecommendations.push({
          desc: linksData.mentions.recommendation,
          badges: [getCategoryBadge("Backlinks"), getPriorityBadge()],
          source: "Mentions",
        });
      }
    }

    // Extract from performanceData - only if there are actual issues (pass: false)
    if (performanceData) {
      // JavaScript errors recommendations - only if failed or has meaningful recommendation
      if (
        performanceData.javascript_errors?.recommendation &&
        !performanceData.javascript_errors?.pass
      ) {
        extractedRecommendations.push({
          desc: performanceData.javascript_errors.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "JavaScript Errors",
        });
      }

      // Compression recommendations - only if failed or has meaningful recommendation
      if (
        performanceData.compression?.recommendation &&
        !performanceData.compression?.pass
      ) {
        extractedRecommendations.push({
          desc: performanceData.compression.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Compression",
        });
      }

      // Resource count recommendations - only if failed or has meaningful recommendation
      if (
        performanceData.resource_count?.recommendation &&
        !performanceData.resource_count?.pass
      ) {
        extractedRecommendations.push({
          desc: performanceData.resource_count.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Resource Count",
        });
      }

      // AMP recommendations - only if failed or has meaningful recommendation
      if (performanceData.amp?.recommendation && !performanceData.amp?.pass) {
        extractedRecommendations.push({
          desc: performanceData.amp.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "AMP",
        });
      }

      // Inline styles recommendations - only if failed or has meaningful recommendation
      if (
        performanceData.inline_styles?.recommendation &&
        !performanceData.inline_styles?.pass
      ) {
        extractedRecommendations.push({
          desc: performanceData.inline_styles.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Inline Styles",
        });
      }

      // Performance timing recommendations - only if failed or has meaningful recommendation
      if (
        performanceData.performance_timing?.recommendation &&
        !performanceData.performance_timing?.pass
      ) {
        extractedRecommendations.push({
          desc: performanceData.performance_timing.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Performance Timing",
        });
      }

      // Image optimization recommendations - only if failed or has meaningful recommendation
      if (
        performanceData.image_optimization?.recommendation &&
        !performanceData.image_optimization?.pass
      ) {
        extractedRecommendations.push({
          desc: performanceData.image_optimization.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Image Optimisation",
        });
      }

      // Minification recommendations - only if failed or has meaningful recommendation
      if (
        performanceData.minification?.recommendation &&
        !performanceData.minification?.pass
      ) {
        extractedRecommendations.push({
          desc: performanceData.minification.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Minification",
        });
      }
    }

    // Extract from pagespeedData - only if there are actual issues (pass: false)
    if (pagespeedData) {
      // Core Web Vitals recommendations - only if failed or has meaningful recommendation
      if (
        pagespeedData.core_web_vitals_desktop?.recommendation &&
        !pagespeedData.core_web_vitals_desktop?.pass
      ) {
        extractedRecommendations.push({
          desc: pagespeedData.core_web_vitals_desktop.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Core Web Vitals (Desktop)",
        });
      }

      // Page speed desktop recommendations - only if failed or has meaningful recommendation
      if (
        pagespeedData.performance_desktop?.recommendation &&
        !pagespeedData.performance_desktop?.pass
      ) {
        extractedRecommendations.push({
          desc: pagespeedData.performance_desktop.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Page Speed Desktop",
        });
      }
    }

    // Extract from pagespeedMobileData - only if there are actual issues (pass: false)
    if (pagespeedMobileData) {
      // Core Web Vitals Mobile recommendations - only if failed or has meaningful recommendation
      if (
        pagespeedMobileData.core_web_vitals_mobile?.recommendation &&
        !pagespeedMobileData.core_web_vitals_mobile?.pass
      ) {
        extractedRecommendations.push({
          desc: pagespeedMobileData.core_web_vitals_mobile.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Core Web Vitals (Mobile)",
        });
      }

      // Page speed mobile recommendations - only if failed or has meaningful recommendation
      if (
        pagespeedMobileData.performance_mobile?.recommendation &&
        !pagespeedMobileData.performance_mobile?.pass
      ) {
        extractedRecommendations.push({
          desc: pagespeedMobileData.performance_mobile.recommendation,
          badges: [getCategoryBadge("Performance"), getPriorityBadge()],
          source: "Page Speed Mobile",
        });
      }
    }

    // Extract from techseoData - only if there are actual issues (pass: false)
    if (techseoData) {
      // Only show recommendations if the overall techseo analysis failed or has specific failed components

      // Robots meta recommendations - RobotsMetaAnalysis doesn't have pass property, so check if noindex is true (which would be an issue)
      if (
        techseoData.robots_meta?.recommendation &&
        techseoData.robots_meta?.noindex
      ) {
        extractedRecommendations.push({
          desc: techseoData.robots_meta.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "Robots Meta",
        });
      }

      // DNS servers recommendations - only if failed or has meaningful recommendation
      if (
        techseoData.dns_servers?.recommendation &&
        !techseoData.dns_servers?.pass
      ) {
        extractedRecommendations.push({
          desc: techseoData.dns_servers.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "DNS Servers",
        });
      }

      // Web server recommendations - only if failed or has meaningful recommendation
      if (
        techseoData.web_server?.recommendation &&
        !techseoData.web_server?.pass
      ) {
        extractedRecommendations.push({
          desc: techseoData.web_server.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "Web Server",
        });
      }

      // Charset recommendations - only if failed or has meaningful recommendation
      if (techseoData.charset?.recommendation && !techseoData.charset?.pass) {
        extractedRecommendations.push({
          desc: techseoData.charset.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "Character Encoding",
        });
      }

      // DMARC record recommendations - only if failed or has meaningful recommendation
      if (
        techseoData.dmarc_record?.recommendation &&
        !techseoData.dmarc_record?.pass
      ) {
        extractedRecommendations.push({
          desc: techseoData.dmarc_record.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "DMARC Record",
        });
      }

      // SPF record recommendations - only if failed or has meaningful recommendation
      if (
        techseoData.spf_record?.recommendation &&
        !techseoData.spf_record?.pass
      ) {
        extractedRecommendations.push({
          desc: techseoData.spf_record.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "SPF Record",
        });
      }

      // Server IP recommendations - only if failed or has meaningful recommendation
      if (
        techseoData.server_ip?.recommendation &&
        !techseoData.server_ip?.pass
      ) {
        extractedRecommendations.push({
          desc: techseoData.server_ip.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "Server IP",
        });
      }

      // Technologies recommendations - only if there are actual issues (failed analysis)
      if (
        techseoData.technologies?.recommendations &&
        !techseoData.technologies?.pass &&
        Array.isArray(techseoData.technologies.recommendations)
      ) {
        techseoData.technologies.recommendations.forEach((rec) => {
          extractedRecommendations.push({
            desc: rec,
            badges: [getCategoryBadge("Technology")],
            source: "Technology",
          });
        });
      }

      // Handle SSL recommendation - only if SSL analysis failed
      if (
        techseoData.ssl_enabled?.recommendation &&
        !techseoData.ssl_enabled?.pass
      ) {
        extractedRecommendations.push({
          desc: techseoData.ssl_enabled.recommendation,
          badges: [getCategoryBadge("Technology")],
          source: "SSL Certificate",
        });
      }
    }

    // Set the recommendations
    setRecommendations(extractedRecommendations);
  }, [
    onPageSeoData,
    usabilityData,
    linksData,
    techseoData,
    socialData,
    performanceData,
    pagespeedData,
    pagespeedMobileData,
  ]);

  // Check if a tab is selected
  const checkSelected = (value: string): boolean => activeTab === value;

  // Handle tab click - simply set the active tab
  const handleTabClick = (value: string) => {
    setActiveTab(value);
  };

  // Helper function to get priority level for sorting (3 for High, 2 for Medium, 1 for Low)
  const getPriorityLevel = (recommendation: Recommendation): number => {
    // For new format with text/priority
    if (
      typeof recommendation.desc === "object" &&
      recommendation.desc !== null &&
      "priority" in recommendation.desc
    ) {
      const priority = recommendation.desc.priority;
      if (typeof priority === "string") {
        if (priority.toLowerCase().includes("high")) return 3;
        if (priority.toLowerCase().includes("medium")) return 2;
        if (priority.toLowerCase().includes("low")) return 1;
      }
      return 2; // Default to medium if priority is unknown
    }

    // For old format with badges
    const priorityBadge = recommendation.badges.find((badge) =>
      badge.label.includes("Priority")
    );

    if (priorityBadge) {
      if (priorityBadge.style === "badge--danger") return 3; // High
      if (priorityBadge.style === "badge--warning") return 2; // Medium
      if (priorityBadge.style === "badge--success") return 1; // Low
    }

    // If no priority found, use score if available
    if (recommendation.score !== undefined) {
      if (recommendation.score <= 3) return 3; // High
      if (recommendation.score <= 6) return 2; // Medium
      return 1; // Low
    }

    return 2; // Default to medium priority
  };

  // Filter recommendations based on active tab
  const filteredRecommendations =
    activeTab === "All"
      ? [...recommendations].sort(
          (a, b) => getPriorityLevel(b) - getPriorityLevel(a)
        )
      : recommendations
          .filter((rec) => {
            // Check if it's the new format with text/priority
            if (
              typeof rec.desc === "object" &&
              rec.desc !== null &&
              "text" in rec.desc
            ) {
              // For new format, check the first badge which should be the category
              return rec.badges.length > 0 && rec.badges[0].label === activeTab;
            }
            // For old format, check all badges
            return rec.badges.some((badge) => badge.label === activeTab);
          })
          .sort((a, b) => getPriorityLevel(b) - getPriorityLevel(a));

  // Helper function to get flag icon based on priority with urgency colors
  const getRecommendationIcon = (recommendation: Recommendation) => {
    // For new format with text/priority
    if (
      typeof recommendation.desc === "object" &&
      recommendation.desc !== null &&
      "priority" in recommendation.desc
    ) {
      const priority = recommendation.desc.priority.toLowerCase();
      if (priority.includes("high")) {
        return (
          <FiFlag className="w-8 h-8 sm:w-10 sm:h-10 text-red-600 drop-shadow-sm" />
        );
      } else if (priority.includes("medium")) {
        return (
          <FiFlag className="w-8 h-8 sm:w-10 sm:h-10 text-orange-500 drop-shadow-sm" />
        );
      } else {
        return (
          <FiFlag className="w-8 h-8 sm:w-10 sm:h-10 text-green-600 drop-shadow-sm" />
        );
      }
    }

    // For old format with badges
    const priorityBadge = recommendation.badges.find((badge) =>
      badge.label.includes("Priority")
    );

    if (priorityBadge) {
      if (priorityBadge.style === "badge--danger") {
        return (
          <FiFlag className="w-8 h-8 sm:w-10 sm:h-10 text-red-600 drop-shadow-sm" />
        );
      } else if (priorityBadge.style === "badge--warning") {
        return (
          <FiFlag className="w-8 h-8 sm:w-10 sm:h-10 text-orange-500 drop-shadow-sm" />
        );
      } else if (priorityBadge.style === "badge--success") {
        return (
          <FiFlag className="w-8 h-8 sm:w-10 sm:h-10 text-green-600 drop-shadow-sm" />
        );
      }
    }

    // Default icon - medium priority
    return (
      <FiFlag className="w-8 h-8 sm:w-10 sm:h-10 text-orange-500 drop-shadow-sm" />
    );
  };

  // Helper function to get category name
  const getCategory = (recommendation: Recommendation): string => {
    if (recommendation.badges.length > 0) {
      return recommendation.badges[0].label;
    }
    return "General";
  };

  // Helper function to get priority text
  const getPriorityText = (recommendation: Recommendation): string => {
    // For new format with text/priority
    if (
      typeof recommendation.desc === "object" &&
      recommendation.desc !== null &&
      "priority" in recommendation.desc
    ) {
      return typeof recommendation.desc.priority === "string"
        ? recommendation.desc.priority.charAt(0).toUpperCase() +
            recommendation.desc.priority.slice(1) +
            " Priority"
        : "Medium Priority";
    }

    // For old format with badges
    const priorityBadge = recommendation.badges.find((badge) =>
      badge.label.includes("Priority")
    );

    if (priorityBadge) {
      return priorityBadge.label;
    }

    return "Medium Priority";
  };

  // Don't render the component if there are no recommendations
  if (recommendations.length === 0) {
    return null;
  }

  return (
    <BoxPrimary title="Recommendations">
      <div className="mt-3 mb-4">
        <InfoCard
          title={`${activeTab} Recommendations`}
          description={
            filteredRecommendations.length === 0
              ? `Great job! No recommendations needed${
                  activeTab === "All" ? "." : ` for ${activeTab}.`
                }`
              : `Found ${filteredRecommendations.length} ${
                  activeTab === "All" ? "" : `${activeTab.toLowerCase()} `
                }recommendations that could improve your website's performance.`
          }
          className="my-2"
        />
      </div>

      {/* Tabs Navigation - Fixed height container to prevent layout shifts */}
      <div className="w-full overflow-x-auto pb-1 mb-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent no-scrollbar">
        <div className="flex min-w-max border-b border-gray-200">
          <TabButton
            label="All"
            value="All"
            isActive={checkSelected("All")}
            onClick={handleTabClick}
            className="min-w-[60px] sm:min-w-[80px] flex-shrink-0"
          />
          <TabButton
            label="On-Page SEO"
            value="On-Page SEO"
            isActive={checkSelected("On-Page SEO")}
            onClick={handleTabClick}
            className="min-w-[90px] sm:min-w-[110px] flex-shrink-0"
          />
          <TabButton
            label="Usability"
            value="Usability"
            isActive={checkSelected("Usability")}
            onClick={handleTabClick}
            className="min-w-[70px] sm:min-w-[90px] flex-shrink-0"
          />
          <TabButton
            label="Performance"
            value="Performance"
            isActive={checkSelected("Performance")}
            onClick={handleTabClick}
            className="min-w-[85px] sm:min-w-[105px] flex-shrink-0"
          />
          <TabButton
            label="Technology"
            value="Technology"
            isActive={checkSelected("Technology")}
            onClick={handleTabClick}
            className="min-w-[75px] sm:min-w-[95px] flex-shrink-0"
          />
          <TabButton
            label="Backlinks"
            value="Backlinks"
            isActive={checkSelected("Backlinks")}
            onClick={handleTabClick}
            className="min-w-[70px] sm:min-w-[90px] flex-shrink-0"
          />
        </div>
      </div>

      {/* Recommendations Content with Animation */}
      <div className="w-full flex flex-col gap-3 rounded-md bg-white border border-gray-200 h-[400px] sm:h-[450px] md:h-[500px] lg:h-[600px] overflow-y-auto p-2 sm:p-3">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15, ease: "easeInOut" }}
            className="flex flex-col gap-4"
          >
            {filteredRecommendations.length > 0 ? (
              filteredRecommendations.map((data, index) => {
                // Create a unique key based on content rather than index
                const uniqueKey = `${activeTab}-${data.source}-${
                  typeof data.desc === "string"
                    ? data.desc.slice(0, 50)
                    : JSON.stringify(data.desc).slice(0, 50)
                }`;
                return (
                  <motion.div
                    key={uniqueKey}
                    initial={{ opacity: 0, scale: 0.98 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      duration: 0.12,
                      ease: "easeOut",
                      delay: index * 0.015,
                    }}
                    className={`recommendation-item p-2.5 sm:p-3 border rounded-lg transition-all duration-200 hover:shadow-md bg-white ${
                      getPriorityText(data).toLowerCase().includes("high")
                        ? "border-red-300 hover:border-red-400"
                        : getPriorityText(data).toLowerCase().includes("medium")
                        ? "border-orange-300 hover:border-orange-400"
                        : "border-green-300 hover:border-green-400"
                    }`}
                  >
                    <div className="flex gap-3 items-start">
                      <div className="flex-shrink-0 mt-1">
                        {getRecommendationIcon(data)}
                      </div>

                      <div className="flex-1">
                        {/* Title and description */}
                        <div className="mb-2">
                          <h5 className="text-sm sm:text-base font-bold text-secondary mb-1.5">
                            {data.source}
                          </h5>
                          <p className="text-sm leading-relaxed text-secondary">
                            {typeof data.desc === "object" &&
                            data.desc !== null &&
                            "text" in data.desc
                              ? data.desc.text
                              : String(data.desc)}
                          </p>
                        </div>

                        {/* Badges */}
                        <div className="flex flex-wrap items-center gap-2 mt-2">
                          {/* Category badge */}
                          <div
                            className={`badge recommendation-badge !border-none rounded-md text-xs recommendation-category ${
                              activeTab === "All" ||
                              activeTab === getCategory(data)
                                ? "active"
                                : ""
                            }`}
                          >
                            {getCategory(data)}
                          </div>

                          {/* Priority badge with enhanced styling */}
                          <div
                            className={`badge recommendation-badge !rounded-md text-xs font-semibold ${
                              getPriorityText(data)
                                .toLowerCase()
                                .includes("high")
                                ? "badge--danger"
                                : getPriorityText(data)
                                    .toLowerCase()
                                    .includes("medium")
                                ? "badge--warning"
                                : "badge--success"
                            }`}
                          >
                            <span className="flex items-center gap-1">
                              {getPriorityText(data)
                                .toLowerCase()
                                .includes("high") && (
                                <span className="w-1.5 h-1.5 bg-red-500 rounded-full animate-pulse"></span>
                              )}
                              {getPriorityText(data)
                                .toLowerCase()
                                .includes("medium") && (
                                <span className="w-1.5 h-1.5 bg-orange-500 rounded-full"></span>
                              )}
                              {getPriorityText(data)
                                .toLowerCase()
                                .includes("low") && (
                                <span className="w-1.5 h-1.5 bg-green-500 rounded-full"></span>
                              )}
                              {getPriorityText(data)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })
            ) : (
              <motion.div
                className="text-center py-8 text-secondary/60 flex items-center justify-center h-full"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <div>
                  <FiFlag className="w-12 h-12 sm:w-16 sm:h-16 text-green-600 mx-auto mb-3 sm:mb-4" />
                  <p className="text-base sm:text-lg font-medium px-4">
                    No recommendations available
                    {activeTab === "All" ? "" : ` for ${activeTab}`}
                  </p>
                </div>
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </BoxPrimary>
  );
}
