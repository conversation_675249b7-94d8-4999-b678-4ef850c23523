import { XIcon } from "@/ui/icons/socialMedia";
import { FaLinkedin, FaFacebookSquare, FaInstagram } from "react-icons/fa";
import Link from "next/link";

type Socials = {
  href: string;
  icon: React.ReactNode;
  title: string;
  ariaLabel: string;
};

export default function FooterSocialMedia() {
  const socials: Socials[] = [
    {
      href: "https://www.linkedin.com/in/seo-analyser-com-au/",
      icon: <FaLinkedin className="w-4.5 h-4.5" />,
      title: "LinkedIn",
      ariaLabel: "Follow SEO Analyser on LinkedIn",
    },
    {
      href: "https://x.com/SEO_ANALYSER",
      icon: <XIcon className="w-4.5 h-4.5" />,
      title: "X (Twitter)",
      ariaLabel: "Follow SEO Analyser on X (Twitter)",
    },
    {
      href: "https://www.instagram.com/seoanalyser.com.au/",
      icon: <FaInstagram className="w-4.5 h-4.5" />,
      title: "Instagram",
      ariaLabel: "Follow SEO Analyser on Instagram",
    },
    {
      href: "https://www.facebook.com/profile.php?id=61573784344442",
      icon: <FaFacebookSquare className="w-4.5 h-4.5" />,
      title: "Facebook",
      ariaLabel: "Follow SEO Analyser on Facebook",
    },
  ];

  return (
    <ul className="flex items-center gap-3">
      {socials.map((item, index) => (
        <li key={index}>
          <Link
            href={item.href}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={item.ariaLabel}
            title={item.title}
            className="w-8 h-8 hover:bg-secondary/10  rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-105"
          >
            {item.icon}
          </Link>
        </li>
      ))}
    </ul>
  );
}
