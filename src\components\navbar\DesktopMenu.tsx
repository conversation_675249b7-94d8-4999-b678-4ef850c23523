"use client";
import { DownIcon } from "@/ui/icons/navigation";
import Link from "next/link";
import React, { useState } from "react";
import { LazyMotion, domAnimation, m } from "framer-motion";
import useOutsideClick from "@/hooks/useOutsideClick";

type Props = {
  menuItems: {
    label: string;
    href: string;
    isDropdown?: boolean;
    isSpecial?: boolean;
    subMenus?: {
      label: string;
      href: string;
      description?: string;
    }[];
  }[];
};

export default function DesktopMenu({ menuItems }: Props) {
  const [subActive, setIsSubActive] = useState<number>(-1);

  return (
    <LazyMotion features={domAnimation}>
      <nav role="navigation" aria-label="Desktop navigation">
        <ul className="text-secondary w-full flex items-center gap-2.5">
          {menuItems.map((item, index) => (
            <MenuItem
              key={index}
              index={index}
              item={item}
              subActive={index === subActive}
              setIsSubActive={setIsSubActive}
            />
          ))}
        </ul>
      </nav>
    </LazyMotion>
  );
}

type MenuItemProps = {
  item: Props["menuItems"][0];
  index: number;
  subActive: boolean;
  setIsSubActive: (index: number) => void;
};

function MenuItem({ item, index, subActive, setIsSubActive }: MenuItemProps) {
  const ref = useOutsideClick(() => setIsSubActive(-1));

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (item.isDropdown) {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        setIsSubActive(subActive ? -1 : index);
      } else if (event.key === "Escape" && subActive) {
        setIsSubActive(-1);
      }
    }
  };

  // Special styling for "Go Pro" link
  if (item.isSpecial) {
    return (
      <m.li
        className="flex items-center leading-5 relative"
        whileHover={{
          scale: 1.1,
          rotate: [0, -2, 2, -1, 1, 0],
        }}
        whileTap={{
          scale: 0.9,
          rotate: 5,
        }}
        transition={{
          scale: { type: "spring", stiffness: 300, damping: 15 },
          rotate: { duration: 0.6, ease: "easeInOut" },
        }}
      >
        <m.div className="relative">
          <m.span
            className="inline-block p-0.5 xl:p-3.5 text-primary font-bold whitespace-nowrap"
            whileHover={{
              color: "rgba(145, 74, 196, 1)",
              letterSpacing: "0.1em",
            }}
            transition={{
              letterSpacing: { duration: 0.3 },
            }}
          >
            <Link
              href={item.href}
              aria-label={`${item.label} - Premium features`}
            >
              {item.label}
            </Link>
          </m.span>
        </m.div>
      </m.li>
    );
  }

  return (
    <li
      className="relative flex items-center leading-5 hover:text-primary duration-300 ease-in-out"
      onMouseEnter={() => setIsSubActive(index)}
      onMouseLeave={() => setIsSubActive(-1)}
    >
      <Link
        href={item.href}
        className="inline-block p-0.5 xl:p-2.5 whitespace-nowrap relative z-10"
        aria-label={item.label}
        aria-expanded={item.isDropdown ? subActive : undefined}
        aria-haspopup={item.isDropdown ? "menu" : undefined}
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        {item.label}
      </Link>
      {item.isDropdown && <DownIcon className="ml-1" aria-hidden="true" />}
      {item.isDropdown && (
        <motion.div
          ref={ref}
          initial={{
            opacity: 0,
            top: "calc(100% + 32px)",
            visibility: "hidden",
          }}
          animate={{
            opacity: subActive ? 1 : 0,
            top: subActive ? "calc(100% + 16px)" : "calc(100% + 32px)",
            visibility: subActive ? "visible" : "hidden",
          }}
          transition={{ duration: 0.3 }}
          className="absolute left-0 w-[1000px] max-w-[95vw] z-50"
          role="menu"
          aria-label={`${item.label} submenu`}
        >
          <div className="w-full bg-white p-6 rounded-2xl">
            {item.subMenus &&
              [...Array(Math.ceil(item.subMenus.length / 4))].map(
                (_, groupIndex) => (
                  <ul
                    key={groupIndex}
                    className="grid grid-cols-4 border-b border-b-light-gray pb-6 mb-6 last:mb-0 last:pb-0 last:border-b-0"
                    role="none"
                  >
                    {(item.subMenus ?? [])
                      .slice(groupIndex * 4, groupIndex * 4 + 4)
                      .map((subItem, subIndex) => (
                        <li
                          key={subIndex}
                          className="flex-1 pr-4 mr-4 last:pr-0 last:mr-0 border-r last:border-r-0 border-light-gray"
                          role="menuitem"
                        >
                          <div className="p-6">
                            <Link
                              href={subItem.href}
                              className="text-secondary hover:text-primary duration-300 font-semibold inline-block mb-2"
                              aria-label={subItem.label}
                            >
                              {subItem.label}
                            </Link>
                            <div className="text-sm text-primary/60">
                              {subItem.description}
                            </div>
                          </div>
                        </li>
                      ))}
                  </ul>
                )
              )}
          </div>
        </motion.div>
      )}
    </li>
  );
}
